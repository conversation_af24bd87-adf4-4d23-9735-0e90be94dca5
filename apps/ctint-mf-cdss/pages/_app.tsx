import { AppProps } from 'next/app';
import Head from 'next/head';
import 'react-datepicker/dist/react-datepicker.css';

import './app.css';
import '@huolala-tech/page-spy-plugin-ospy/dist/index.css';
import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import Script from 'next/script';
import {
  initI18Next,
  addLocalesResources,
} from '@cdss-modules/design-system/i18n/client';
import { prepareLocales } from '../i18n/locales';
import { PermissionProvider } from '@cdss-modules/design-system/context/PermissionProvider';
import { useEffect } from 'react';
// import LoadPageSpy from './load-page-spy';

declare global {
  interface Window {
    GLOBAL_ENV_VARS: any;
    GLOBAL_BASE_PATH: any;
    GLOBAL_MF_NAME: any;
  }
}

function CustomApp({ Component, pageProps }: AppProps) {
  initI18Next(
    (langauges: string[] = []) => {
      langauges.forEach((lang) => {
        prepareLocales((resultLocale: any) =>
          addLocalesResources(resultLocale[lang], lang)
        );
      });
    },
    undefined,
    pageProps?.globalConfig?.languages
  );

  // set the tenant to localStorage
  useEffect(() => {
    // init pagespy - 只在客户端环境中初始化
    if (typeof window !== 'undefined') {
      // 动态导入 OSpy
      import('@huolala-tech/page-spy-plugin-ospy')
        .then((module) => {
          const OSpy = module.default;
          new OSpy();
        })
        .catch(console.error);
    }

    if (pageProps?.globalConfig?.microfrontends['tenant']) {
      localStorage.setItem(
        'tenant',
        pageProps?.globalConfig?.microfrontends['tenant']
      );
    } else {
      localStorage.setItem('tenant', 'ctint');
    }

    // set the error code to localStorage for should logout
    if (pageProps?.globalConfig?.microfrontends['errorCode']) {
      console.log(
        'errorCode',
        pageProps?.globalConfig?.microfrontends['errorCode']
      );
      localStorage.setItem(
        'errorCode',
        pageProps?.globalConfig?.microfrontends['errorCode']
      );
    }
  }, []);

  return (
    <>
      <Head>
        <title>Welcome to CDSS!</title>
      </Head>
      <Script id="GLOBAL_CONFIG_VARS">
        {`window.GLOBAL_ENV_VARS = ${JSON.stringify(pageProps.publicEnvVars)};`}
      </Script>
      {/* <LoadPageSpy /> */}
      <PermissionProvider>
        <main className="app">
          <Component {...pageProps} />
          <Toaster />
        </main>
      </PermissionProvider>
    </>
  );
}

export default CustomApp;
