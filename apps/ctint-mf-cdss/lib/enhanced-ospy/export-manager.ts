/**
 * 导出管理器
 * 处理会话数据的导出功能，保持与原有格式兼容
 */

import { OSpySessionDB, SessionData } from './session-db';
import { EnhancedOSpy } from './enhanced-ospy';

export class ExportManager {
  private sessionDB: OSpySessionDB;

  constructor(sessionDB: OSpySessionDB) {
    this.sessionDB = sessionDB;
  }

  /**
   * 导出当前会话
   */
  async exportCurrentSession(enhancedOSpy: EnhancedOSpy): Promise<void> {
    const currentSession = enhancedOSpy.getCurrentSession();
    if (!currentSession) {
      throw new Error('当前没有活动会话可导出');
    }

    // 获取当前 o-spy 数据
    const currentData = await this.getOSpyData(enhancedOSpy);
    
    // 保持与原有格式兼容
    const exportData = {
      ...currentData,
      sessionInfo: {
        id: currentSession.sessionId,
        startTime: new Date(currentSession.startTime).toISOString(),
        status: 'current',
        duration: Date.now() - currentSession.startTime,
        url: currentSession.metadata.url,
        title: currentSession.metadata.title,
        userId: currentSession.metadata.userId
      }
    };
    
    const filename = `ospy-current-session-${Date.now()}.json`;
    this.downloadJSON(exportData, filename);
    
    console.log('当前会话导出完成:', filename);
  }

  /**
   * 导出历史会话
   */
  async exportHistorySession(sessionId: string): Promise<void> {
    const session = await this.sessionDB.getSession(sessionId);
    if (!session) {
      throw new Error('会话不存在');
    }

    const exportData = {
      sessionInfo: {
        id: session.sessionId,
        startTime: new Date(session.startTime).toISOString(),
        endTime: session.endTime ? new Date(session.endTime).toISOString() : null,
        status: session.status,
        duration: session.endTime ? session.endTime - session.startTime : null,
        url: session.metadata.url,
        title: session.metadata.title,
        userId: session.metadata.userId
      },
      metadata: session.metadata,
      recordingData: session.data
    };
    
    const filename = `ospy-session-${sessionId}.json`;
    this.downloadJSON(exportData, filename);
    
    console.log('历史会话导出完成:', filename);
  }

  /**
   * 批量导出会话
   */
  async exportMultipleSessions(sessionIds: string[]): Promise<void> {
    const sessions = await Promise.all(
      sessionIds.map(id => this.sessionDB.getSession(id))
    );
    
    const validSessions = sessions.filter(session => session !== null) as SessionData[];
    
    if (validSessions.length === 0) {
      throw new Error('没有有效的会话可导出');
    }

    const exportData = {
      exportInfo: {
        exportTime: new Date().toISOString(),
        totalSessions: validSessions.length,
        exportType: 'batch'
      },
      sessions: validSessions.map(session => ({
        sessionInfo: {
          id: session.sessionId,
          startTime: new Date(session.startTime).toISOString(),
          endTime: session.endTime ? new Date(session.endTime).toISOString() : null,
          status: session.status,
          duration: session.endTime ? session.endTime - session.startTime : null,
          url: session.metadata.url,
          title: session.metadata.title,
          userId: session.metadata.userId
        },
        metadata: session.metadata,
        recordingData: session.data
      }))
    };
    
    const filename = `ospy-batch-export-${Date.now()}.json`;
    this.downloadJSON(exportData, filename);
    
    console.log(`批量导出完成: ${validSessions.length} 个会话`);
  }

  /**
   * 导出所有会话
   */
  async exportAllSessions(): Promise<void> {
    const sessions = await this.sessionDB.getAllSessions();
    
    if (sessions.length === 0) {
      throw new Error('没有会话可导出');
    }

    const exportData = {
      exportInfo: {
        exportTime: new Date().toISOString(),
        totalSessions: sessions.length,
        exportType: 'all',
        dateRange: {
          from: new Date(Math.min(...sessions.map(s => s.timestamp))).toISOString(),
          to: new Date(Math.max(...sessions.map(s => s.timestamp))).toISOString()
        }
      },
      sessions: sessions.map(session => ({
        sessionInfo: {
          id: session.sessionId,
          startTime: new Date(session.startTime).toISOString(),
          endTime: session.endTime ? new Date(session.endTime).toISOString() : null,
          status: session.status,
          duration: session.endTime ? session.endTime - session.startTime : null,
          url: session.metadata.url,
          title: session.metadata.title,
          userId: session.metadata.userId
        },
        metadata: session.metadata,
        recordingData: session.data
      }))
    };
    
    const filename = `ospy-all-sessions-${Date.now()}.json`;
    this.downloadJSON(exportData, filename);
    
    console.log(`全部会话导出完成: ${sessions.length} 个会话`);
  }

  /**
   * 导出会话统计报告
   */
  async exportSessionReport(): Promise<void> {
    const sessions = await this.sessionDB.getAllSessions();
    const stats = await this.sessionDB.getStats();
    
    // 按状态分组
    const sessionsByStatus = sessions.reduce((acc, session) => {
      acc[session.status] = (acc[session.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 按日期分组
    const sessionsByDate = sessions.reduce((acc, session) => {
      const date = new Date(session.timestamp).toDateString();
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 计算平均会话时长
    const completedSessions = sessions.filter(s => s.status === 'completed' && s.endTime);
    const avgDuration = completedSessions.length > 0 
      ? completedSessions.reduce((sum, s) => sum + (s.endTime! - s.startTime), 0) / completedSessions.length
      : 0;

    const reportData = {
      reportInfo: {
        generatedAt: new Date().toISOString(),
        reportType: 'session-statistics',
        period: {
          from: stats.oldestSession?.toISOString(),
          to: stats.newestSession?.toISOString()
        }
      },
      summary: {
        totalSessions: stats.totalSessions,
        recordingSessions: stats.recordingSessions,
        completedSessions: stats.completedSessions,
        interruptedSessions: sessions.filter(s => s.status === 'interrupted').length,
        averageDuration: Math.round(avgDuration / 1000), // 转换为秒
        averageDurationFormatted: this.formatDuration(avgDuration)
      },
      breakdown: {
        byStatus: sessionsByStatus,
        byDate: sessionsByDate
      },
      topPages: this.getTopPages(sessions),
      recentSessions: sessions.slice(0, 10).map(session => ({
        id: session.sessionId,
        startTime: new Date(session.startTime).toISOString(),
        status: session.status,
        url: session.metadata.url,
        title: session.metadata.title,
        duration: session.endTime ? session.endTime - session.startTime : null
      }))
    };
    
    const filename = `ospy-session-report-${Date.now()}.json`;
    this.downloadJSON(reportData, filename);
    
    console.log('会话统计报告导出完成');
  }

  /**
   * 获取访问最多的页面
   */
  private getTopPages(sessions: SessionData[]): Array<{url: string, title: string, count: number}> {
    const pageCount = sessions.reduce((acc, session) => {
      const key = `${session.metadata.url}|${session.metadata.title}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(pageCount)
      .map(([key, count]) => {
        const [url, title] = key.split('|');
        return { url, title, count };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * 格式化持续时间
   */
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * 获取 O-Spy 数据
   */
  private async getOSpyData(enhancedOSpy: EnhancedOSpy): Promise<any> {
    try {
      // 这里需要根据实际的 o-spy API 来获取数据
      // 暂时返回一个模拟的数据结构
      return {
        console: [],
        network: [],
        storage: {},
        system: {},
        page: {
          url: window.location.href,
          title: document.title,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      console.error('获取 O-Spy 数据失败:', error);
      return {};
    }
  }

  /**
   * 下载 JSON 文件
   */
  private downloadJSON(data: any, filename: string): void {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理 URL 对象
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 100);
      
    } catch (error) {
      console.error('下载文件失败:', error);
      throw new Error('文件下载失败');
    }
  }

  /**
   * 验证导出数据格式
   */
  private validateExportData(data: any): boolean {
    try {
      // 基本验证
      if (!data || typeof data !== 'object') {
        return false;
      }

      // 检查必要字段
      if (data.sessionInfo && typeof data.sessionInfo === 'object') {
        return true;
      }

      if (data.sessions && Array.isArray(data.sessions)) {
        return true;
      }

      return false;
    } catch {
      return false;
    }
  }
}
